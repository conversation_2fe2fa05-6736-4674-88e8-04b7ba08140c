import { useEffect } from 'react';
import { useLocation } from 'react-router-dom';

/**
 * مكون ScrollToTop - يقوم بالتمرير التلقائي لأعلى الصفحة عند تغيير المسار
 * 
 * هذا المكون يستمع لتغييرات المسار ويقوم بالتمرير لأعلى الصفحة تلقائياً
 * عند الانتقال إلى صفحة جديدة، مما يحسن تجربة المستخدم
 */
const ScrollToTop: React.FC = () => {
  const { pathname } = useLocation();

  useEffect(() => {
    // التمرير لأعلى الصفحة عند تغيير المسار
    window.scrollTo({
      top: 0,
      left: 0,
      behavior: 'smooth' // تمرير سلس وناعم
    });
  }, [pathname]); // يتم تشغيل التأثير عند تغيير pathname

  // هذا المكون لا يعرض أي محتوى مرئي
  return null;
};

export default ScrollToTop;
